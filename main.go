package main

import (
	"ai-clerk/handlers"
	"ai-clerk/middleware"
	"ai-clerk/utils"
	"log"

	"github.com/labstack/echo/v4"
	echomiddleware "github.com/labstack/echo/v4/middleware"
	"github.com/spf13/viper"
)

func main() {
	// 初始化配置
	initConfig()

	// 初始化数据库
	utils.InitDB()
	utils.InitRedis()

	// 自动迁移数据库表
	// 在这里添加你的模型进行自动迁移
	// 例如：
	// if err := utils.DB.AutoMigrate(&models.User{}, &models.App{}); err != nil {
	//     log.Fatal("Failed to migrate database:", err)
	// }

	// 创建 Echo 实例
	e := echo.New()

	// 中间件
	e.Use(echomiddleware.Logger())
	e.Use(echomiddleware.Recover())
	e.Use(echomiddleware.CORS())

	// 路由
	setupRoutes(e)

	// 启动服务器
	port := viper.GetString("server.port")
	log.Printf("Server starting on port %s", port)
	e.Logger.Fatal(e.Start(port))
}

func initConfig() {
	viper.SetConfigName("config")
	viper.SetConfigType("toml")
	viper.AddConfigPath(".")

	if err := viper.ReadInConfig(); err != nil {
		log.Fatal("Error reading config file:", err)
	}
}

func setupRoutes(e *echo.Echo) {
	// 健康检查
	e.GET("/health", func(c echo.Context) error {
		return c.JSON(200, map[string]string{"status": "ok"})
	})

	// API 路由组
	api := e.Group("/api/v1")

	// 示例路由 - 可以根据需要修改
	userHandler := &handlers.UserHandler{}
	api.GET("/user/example", userHandler.Example)

	appHandler := &handlers.AppHandler{}
	api.GET("/app/example", appHandler.Example)

	// 需要认证的路由示例
	auth := api.Group("")
	auth.Use(middleware.JWTAuth())
	auth.GET("/protected", func(c echo.Context) error {
		userID := middleware.GetCurrentUserID(c)
		return c.JSON(200, map[string]any{
			"message": "This is a protected route",
			"user_id": userID,
		})
	})
}
