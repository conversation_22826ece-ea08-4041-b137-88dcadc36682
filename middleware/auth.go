package middleware

import (
	"ai-clerk/utils"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
)

// JWT 认证中间件
func JWTAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				return utils.ErrorResponse(c, http.StatusUnauthorized, "Missing authorization header")
			}

			// 检查 Bearer 前缀
			tokenString := strings.TrimPrefix(authHeader, "Bearer ")
			if tokenString == authHeader {
				return utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid authorization header format")
			}

			// 验证 token
			claims, err := utils.ValidateToken(tokenString)
			if err != nil {
				return utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid token")
			}

			// 将用户信息存储到上下文中
			c.Set("user_id", claims.UserID)
			c.Set("user_email", claims.Email)

			return next(c)
		}
	}
}

// 获取当前用户ID
func GetCurrentUserID(c echo.Context) uint {
	userID := c.Get("user_id")
	if userID == nil {
		return 0
	}
	return userID.(uint)
}
