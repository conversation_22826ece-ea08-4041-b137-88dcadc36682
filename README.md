# AI-Clerk

基于 Echo 和 GORM 的简单 Web 项目框架。

## 项目结构

```
ai-clerk/
├── main.go              # 主入口文件
├── config.toml          # 配置文件
├── go.mod              # Go 模块文件
├── handlers/           # 处理器（合并了 handler 和 service）
│   ├── user.go         # 用户相关处理
│   └── app.go          # 应用相关处理
├── models/             # 数据模型
│   ├── user.go         # 用户模型
│   └── app.go          # 应用模型
├── middleware/         # 中间件
│   └── auth.go         # 认证中间件
└── utils/              # 工具类
    ├── db.go           # 数据库工具
    └── redis.go        # Redis 工具
```

## 功能特性

- ✅ Echo Web 框架
- ✅ GORM 数据库 ORM
- ✅ JWT 认证中间件
- ✅ Redis 缓存支持
- ✅ 数据库连接池
- ✅ 统一错误处理
- ✅ 分页查询工具
- ✅ 配置文件管理
- ✅ Docker 支持

## 快速开始

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 配置数据库

修改 `config.toml` 文件中的数据库配置：

```toml
[database]
host = "localhost"
port = 3306
user = "root"
password = "your_password"
dbname = "ai_clerk"
charset = "utf8mb4"
```

### 3. 创建数据库

```sql
CREATE DATABASE ai_clerk CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 添加你的模型

在 `models/` 目录下定义你的数据模型，然后在 `main.go` 中添加自动迁移：

```go
// 在 main.go 中取消注释并添加你的模型
if err := utils.DB.AutoMigrate(&models.User{}, &models.App{}); err != nil {
    log.Fatal("Failed to migrate database:", err)
}
```

### 5. 实现你的处理器

在 `handlers/` 目录下实现你的业务逻辑，然后在 `main.go` 的 `setupRoutes` 函数中添加路由。

### 6. 启动服务

```bash
go run main.go
```

服务将在 `http://localhost:8080` 启动。

## 示例 API

### 健康检查

```bash
curl http://localhost:8080/health
```

### 示例路由

```bash
curl http://localhost:8080/api/v1/user/example
curl http://localhost:8080/api/v1/app/example
```

### 受保护的路由

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8080/api/v1/protected
```

## 配置说明

### 服务器配置

```toml
[server]
port = ":8080"    # 服务端口
debug = true      # 调试模式
```

### 数据库配置

```toml
[database]
host = "localhost"
port = 3306
user = "root"
password = "password"
dbname = "ai_clerk"
charset = "utf8mb4"
```

### Redis 配置

```toml
[redis]
addr = "localhost:6379"
password = ""
db = 0
```

### JWT 配置

```toml
[jwt]
secret = "your-secret-key-here"
expire_hours = 24
```

## 开发说明

### 项目特点

1. **扁平化结构**：避免过度拆分，保持项目结构简单
2. **合并 Handler 和 Service**：减少层级，提高开发效率
3. **统一错误处理**：使用中间件统一处理错误响应
4. **完整的认证框架**：JWT 认证中间件和工具函数
5. **数据库自动迁移**：支持 GORM 自动迁移

### 扩展建议

- 根据需要在 `models/` 中定义数据模型
- 在 `handlers/` 中实现业务逻辑
- 在 `middleware/` 中添加自定义中间件
- 使用 `utils/` 中的数据库和 Redis 工具
- 添加数据验证、日志记录、单元测试等

## Docker 部署

```bash
# 使用 Docker Compose 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f ai-clerk
```

## 许可证

MIT License
