package models

import (
	"time"

	"gorm.io/gorm"
)

// 示例模型 - 可以根据需要修改或删除
type App struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 在这里添加你的应用字段
	// 例如：
	// Name   string `json:"name" gorm:"not null"`
	// UserID uint   `json:"user_id" gorm:"not null;index"`
}
