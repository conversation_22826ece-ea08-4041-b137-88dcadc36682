package models

import (
	"time"

	"gorm.io/gorm"
)

type User struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 在这里添加你的用户字段
	// 例如：
	// Username string `json:"username" gorm:"uniqueIndex;not null"`
	// Email    string `json:"email" gorm:"uniqueIndex;not null"`
	// Password string `json:"-" gorm:"not null"`
}
